#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

const SQL = `
-- EBITDA map
CREATE TABLE IF NOT EXISTS ebitda_map (
  id BIGSERIAL PRIMARY KEY,
  account_id TEXT UNIQUE,
  account_sub_type TEXT,
  name_pattern TEXT,
  bucket TEXT NOT NULL CHECK (bucket IN ('Interest','Taxes','Depreciation','Amortization')),
  priority INT NOT NULL DEFAULT 100
);

-- KPIs
CREATE TABLE IF NOT EXISTS pnl_kpis (
  id BIGSERIAL PRIMARY KEY,
  report_id BIGINT NOT NULL UNIQUE REFERENCES pnl_reports(id) ON DELETE CASCADE,
  net_income NUMERIC(18,2) NOT NULL,
  interest_expense NUMERIC(18,2) NOT NULL DEFAULT 0,
  income_taxes NUMERIC(18,2) NOT NULL DEFAULT 0,
  depreciation NUMERIC(18,2) NOT NULL DEFAULT 0,
  amortization NUMERIC(18,2) NOT NULL DEFAULT 0,
  ebit NUMERIC(18,2) NOT NULL,
  ebitda NUMERIC(18,2) NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- View for mapping
CREATE OR REPLACE VIEW v_pnl_ebitda_buckets AS
WITH L AS (
  SELECT l.id, l.report_id, l.account_id, l.account_name, l.amount, a.account_sub_type
  FROM pnl_lines l
  LEFT JOIN accounts a ON a.id = l.account_id
),
M AS (
  SELECT L.id AS line_id,
    COALESCE(
      (SELECT bucket FROM ebitda_map m WHERE m.account_id = L.account_id ORDER BY m.priority ASC LIMIT 1),
      (SELECT bucket FROM ebitda_map m WHERE m.account_sub_type = L.account_sub_type ORDER BY m.priority ASC LIMIT 1),
      (SELECT bucket FROM ebitda_map m WHERE L.account_name ILIKE m.name_pattern ORDER BY m.priority ASC LIMIT 1)
    ) AS bucket
  FROM L
)
SELECT L.report_id, L.account_id, L.account_name, L.amount, L.account_sub_type, M.bucket
FROM L LEFT JOIN M ON M.line_id = L.id;

-- View for EBITDA calc
CREATE OR REPLACE VIEW v_kpi_ebitda AS
WITH ni AS (
  SELECT report_id, amount AS net_income
  FROM pnl_summaries
  WHERE "group" = 'NetIncome'
),
agg AS (
  SELECT report_id,
    SUM(CASE WHEN bucket='Interest'     THEN amount ELSE 0 END) AS interest_expense,
    SUM(CASE WHEN bucket='Taxes'        THEN amount ELSE 0 END) AS income_taxes,
    SUM(CASE WHEN bucket='Depreciation' THEN amount ELSE 0 END) AS depreciation,
    SUM(CASE WHEN bucket='Amortization' THEN amount ELSE 0 END) AS amortization
  FROM v_pnl_ebitda_buckets
  GROUP BY report_id
)
SELECT n.report_id, n.net_income,
       COALESCE(a.interest_expense,0) AS interest_expense,
       COALESCE(a.income_taxes,0)     AS income_taxes,
       COALESCE(a.depreciation,0)     AS depreciation,
       COALESCE(a.amortization,0)     AS amortization,
       (n.net_income + COALESCE(a.interest_expense,0) + COALESCE(a.income_taxes,0)) AS ebit,
       (n.net_income + COALESCE(a.interest_expense,0) + COALESCE(a.income_taxes,0)
                    + COALESCE(a.depreciation,0) + COALESCE(a.amortization,0)) AS ebitda
FROM ni n LEFT JOIN agg a ON a.report_id = n.report_id;
`;

async function run() {
  await pg.connect();
  await pg.query(SQL);
  console.log("✅ Schema, tables, and views ready.");
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
