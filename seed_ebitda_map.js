#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

const rows = [
  {
    account_sub_type: "InterestExpense",
    name_pattern: null,
    bucket: "Interest",
  },
  {
    account_sub_type: null,
    name_pattern: "interest expense%",
    bucket: "Interest",
  },
  { account_sub_type: "IncomeTaxExpense", name_pattern: null, bucket: "Taxes" },
  { account_sub_type: null, name_pattern: "income tax%", bucket: "Taxes" },
  {
    account_sub_type: "Depreciation",
    name_pattern: null,
    bucket: "Depreciation",
  },
  {
    account_sub_type: null,
    name_pattern: "depreciation%",
    bucket: "Depreciation",
  },
  {
    account_sub_type: "Amortization",
    name_pattern: null,
    bucket: "Amortization",
  },
  {
    account_sub_type: null,
    name_pattern: "amortization%",
    bucket: "Amortization",
  },
];

async function run() {
  await pg.connect();
  for (const r of rows) {
    await pg.query(
      `INSERT INTO ebitda_map (account_id, account_sub_type, name_pattern, bucket)
       VALUES ($1,$2,$3,$4) ON CONFLICT DO NOTHING`,
      [null, r.account_sub_type, r.name_pattern, r.bucket]
    );
  }
  console.log("✅ Seeded ebitda_map with defaults.");
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
