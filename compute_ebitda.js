#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const REPORT_ID = process.argv[2]; // pass report_id or leave empty for latest

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

const SQL = `
WITH pick AS (
  SELECT COALESCE($1::bigint, (SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1)) AS report_id
),
k AS (
  SELECT * FROM v_kpi_ebitda WHERE report_id = (SELECT report_id FROM pick)
)
INSERT INTO pnl_kpis (report_id, net_income, interest_expense, income_taxes, depreciation, amortization, ebit, ebitda)
SELECT report_id, net_income, interest_expense, income_taxes, depreciation, amortization, ebit, ebitda
FROM k
ON CONFLICT (report_id) DO UPDATE SET
  net_income = EXCLUDED.net_income,
  interest_expense = EXCLUDED.interest_expense,
  income_taxes = EXCLUDED.income_taxes,
  depreciation = EXCLUDED.depreciation,
  amortization = EXCLUDED.amortization,
  ebit = EXCLUDED.ebit,
  ebitda = EXCLUDED.ebitda
RETURNING *;
`;

async function run() {
  await pg.connect();
  const { rows } = await pg.query(SQL, [REPORT_ID || null]);
  console.log("✅ EBITDA saved:", rows[0]);
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
