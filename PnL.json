{"Header": {"Time": "2025-09-04T21:34:16-07:00", "ReportName": "ProfitAndLoss", "ReportBasis": "Cash", "StartPeriod": "2025-07-01", "EndPeriod": "2025-07-31", "SummarizeColumnsBy": "Total", "Currency": "USD", "Option": [{"Name": "AccountingStandard", "Value": "GAAP"}, {"Name": "NoReportData", "Value": "false"}]}, "Columns": {"Column": [{"ColTitle": "", "ColType": "Account", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "account"}]}, {"ColTitle": "Total", "ColType": "Money", "MetaData": [{"Name": "<PERSON><PERSON><PERSON>", "Value": "total"}]}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Income"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Practice Income", "id": "9"}, {"value": ""}]}, "Rows": {"Row": [{"Header": {"ColData": [{"value": "Dr. <PERSON> - Inc<PERSON>", "id": "251"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "310"}, {"value": "54733.22"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. Canner - Income"}, {"value": "54733.22"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Dr. <PERSON> <PERSON><PERSON>", "id": "315"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "322"}, {"value": "574.77"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. George - Income"}, {"value": "574.77"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "<PERSON>. <PERSON><PERSON> <PERSON> <PERSON><PERSON>", "id": "259"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "334"}, {"value": "68862.52"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. Ghosh - Income"}, {"value": "68862.52"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Dr. <PERSON><PERSON>", "id": "316"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "320"}, {"value": "52032.26"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. Pierce - Income"}, {"value": "52032.26"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "<PERSON>. <PERSON> - <PERSON><PERSON>", "id": "317"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "328"}, {"value": "46853.17"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. <PERSON> - Income"}, {"value": "46853.17"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Dr. <PERSON> <PERSON><PERSON>", "id": "255"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "314"}, {"value": "45509.99"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. Shah - Income"}, {"value": "45509.99"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Dr. <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "id": "347"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "348"}, {"value": "50285.98"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Dr. <PERSON> - Income"}, {"value": "50285.98"}]}, "type": "Section"}, {"Header": {"ColData": [{"value": "Office - Staff Income", "id": "303"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Office Income", "id": "330"}, {"value": "8766.48"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Office - Staff Income"}, {"value": "8766.48"}]}, "type": "Section"}]}, "Summary": {"ColData": [{"value": "Total Practice Income"}, {"value": "327618.39"}]}, "type": "Section"}, {"ColData": [{"value": "Unapplied Cash Payment Income", "id": "257"}, {"value": "947.15"}], "type": "Data"}, {"ColData": [{"value": "Uncategorized Income", "id": "231"}, {"value": "0.00"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Income"}, {"value": "328565.54"}]}, "type": "Section", "group": "Income"}, {"Summary": {"ColData": [{"value": "Gross Profit"}, {"value": "328565.54"}]}, "type": "Section", "group": "GrossProfit"}, {"Header": {"ColData": [{"value": "Expenses"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Bank Charge", "id": "34"}, {"value": "1146.21"}], "type": "Data"}, {"ColData": [{"value": "Cleaning - office", "id": "110"}, {"value": "1160.00"}], "type": "Data"}, {"ColData": [{"value": "Clinical Tool Licensure", "id": "203"}, {"value": "240.00"}], "type": "Data"}, {"ColData": [{"value": "Doctor <PERSON><PERSON>pense - Canner", "id": "**********"}, {"value": "112.50"}], "type": "Data"}, {"ColData": [{"value": "Doctor <PERSON><PERSON> - <PERSON><PERSON><PERSON>", "id": "**********"}, {"value": "525.00"}], "type": "Data"}, {"ColData": [{"value": "Doctor <PERSON><PERSON> Expense - Pierce", "id": "**********"}, {"value": "562.50"}], "type": "Data"}, {"ColData": [{"value": "Doctor <PERSON><PERSON> - <PERSON><PERSON>r", "id": "**********"}, {"value": "712.50"}], "type": "Data"}, {"ColData": [{"value": "Doctor <PERSON><PERSON> Expense - <PERSON>", "id": "**********"}, {"value": "2512.50"}], "type": "Data"}, {"ColData": [{"value": "Dues, Sub & Lic Deb Ghosh", "id": "237"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - Dr<PERSON>", "id": "163"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - Dr<PERSON>", "id": "54"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - <PERSON><PERSON>", "id": "136"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - <PERSON><PERSON>", "id": "137"}, {"value": "850.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - <PERSON><PERSON>", "id": "185"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Dues, Subs & Lic - <PERSON><PERSON>", "id": "355"}, {"value": "175.00"}], "type": "Data"}, {"ColData": [{"value": "Equipment Maint. & Repair", "id": "114"}, {"value": "370.37"}], "type": "Data"}, {"ColData": [{"value": "Health Insurance - <PERSON><PERSON>", "id": "149"}, {"value": "2933.03"}], "type": "Data"}, {"ColData": [{"value": "Health Insurance - Employee", "id": "108"}, {"value": "454.14"}], "type": "Data"}, {"ColData": [{"value": "Health Insurance - <PERSON><PERSON>", "id": "371"}, {"value": "865.71"}], "type": "Data"}, {"ColData": [{"value": "Interest Expense", "id": "72"}, {"value": "3880.41"}], "type": "Data"}, {"ColData": [{"value": "Legal & Accounting", "id": "29"}, {"value": "1050.00"}], "type": "Data"}, {"ColData": [{"value": "Meals & Entertainment", "id": "286"}, {"value": "136.32"}], "type": "Data"}, {"ColData": [{"value": "Medical Supplies", "id": "10"}, {"value": "1435.54"}], "type": "Data"}, {"ColData": [{"value": "Office Expenses", "id": "27"}, {"value": "3842.09"}], "type": "Data"}, {"ColData": [{"value": "Patient Notification Tools", "id": "204"}, {"value": "113.32"}], "type": "Data"}, {"Header": {"ColData": [{"value": "Payroll Expenses", "id": "6"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Payroll Tax - Associate Dr<PERSON>", "id": "361"}, {"value": "735.58"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Dr<PERSON> <PERSON>", "id": "281"}, {"value": "794.42"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON>", "id": "358"}, {"value": "3825.00"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON> <PERSON>", "id": "283"}, {"value": "823.86"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - <PERSON><PERSON>", "id": "284"}, {"value": "617.88"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Dr<PERSON>", "id": "285"}, {"value": "617.88"}], "type": "Data"}, {"ColData": [{"value": "Payroll Tax - Office Staff", "id": "111"}, {"value": "5653.29"}], "type": "Data"}, {"Header": {"ColData": [{"value": "Salaries", "id": "16"}, {"value": ""}]}, "Rows": {"Row": [{"ColData": [{"value": "Payroll - Office Staff", "id": "269"}, {"value": "71030.68"}], "type": "Data"}, {"ColData": [{"value": "Salary - Associate Dr<PERSON> <PERSON><PERSON>", "id": "369"}, {"value": "9615.38"}], "type": "Data"}, {"ColData": [{"value": "Salary - <PERSON><PERSON>", "id": "247"}, {"value": "10384.62"}], "type": "Data"}, {"ColData": [{"value": "Salary - <PERSON><PERSON>", "id": "359"}, {"value": "50000.00"}], "type": "Data"}, {"ColData": [{"value": "Salary - <PERSON><PERSON>", "id": "248"}, {"value": "10769.24"}], "type": "Data"}, {"ColData": [{"value": "Salary - <PERSON><PERSON>", "id": "249"}, {"value": "8076.92"}], "type": "Data"}, {"ColData": [{"value": "Salary - <PERSON>. <PERSON>", "id": "246"}, {"value": "8076.92"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Salaries"}, {"value": "167953.76"}]}, "type": "Section"}]}, "Summary": {"ColData": [{"value": "Total Payroll Expenses"}, {"value": "181021.67"}]}, "type": "Section"}, {"ColData": [{"value": "Practice Mgmt Software Lease", "id": "180"}, {"value": "7613.01"}], "type": "Data"}, {"ColData": [{"value": "Profit Sharing", "id": "73"}, {"value": "13708.33"}], "type": "Data"}, {"ColData": [{"value": "Refunds", "id": "127"}, {"value": "604.61"}], "type": "Data"}, {"ColData": [{"value": "Reimbursement", "id": "131"}, {"value": "980.50"}], "type": "Data"}, {"ColData": [{"value": "Rent", "id": "12"}, {"value": "12054.83"}], "type": "Data"}, {"ColData": [{"value": "Service fee", "id": "221"}, {"value": "207.23"}], "type": "Data"}, {"ColData": [{"value": "Telephone Office", "id": "25"}, {"value": "752.89"}], "type": "Data"}, {"ColData": [{"value": "Utilities", "id": "13"}, {"value": "260.00"}], "type": "Data"}, {"ColData": [{"value": "Vaccines", "id": "20"}, {"value": "54670.83"}], "type": "Data"}, {"ColData": [{"value": "Website Expense", "id": "205"}, {"value": "665.00"}], "type": "Data"}, {"ColData": [{"value": "Workmans Comp Ins", "id": "37"}, {"value": "686.04"}], "type": "Data"}]}, "Summary": {"ColData": [{"value": "Total Expenses"}, {"value": "297177.08"}]}, "type": "Section", "group": "Expenses"}, {"Summary": {"ColData": [{"value": "Net Operating Income"}, {"value": "31388.46"}]}, "type": "Section", "group": "NetOperatingIncome"}, {"Summary": {"ColData": [{"value": "Net Income"}, {"value": "31388.46"}]}, "type": "Section", "group": "NetIncome"}]}}