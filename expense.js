#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const pg = new Client({
  host: process.env.PGHOST,
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
});

const REPORT_ID = process.argv[2]; // pass a specific report_id or leave empty to use latest

async function main() {
  await pg.connect();

  // pick report_id
  let reportId = REPORT_ID;
  if (!reportId) {
    const { rows } = await pg.query(
      `SELECT id FROM pnl_reports ORDER BY id DESC LIMIT 1`
    );
    if (!rows.length) throw new Error("No pnl_reports found");
    reportId = rows[0].id;
  }

  const sql = `
    WITH exp AS (
  SELECT report_id, path, account_name, COALESCE(amount,0) AS amount
  FROM pnl_lines
  WHERE path LIKE 'Expenses%' AND 
    report_id = $1
)
SELECT
  report_id,
  SUM(CASE
        WHEN path ILIKE '%Payroll Expenses%'
          OR account_name ILIKE 'salary %'
          OR account_name ILIKE 'payroll %'
          OR account_name ILIKE 'workmans comp%' 
          OR account_name ILIKE 'profit sharing%'
        THEN amount ELSE 0 END) AS payroll_expenses,
  SUM(CASE
        WHEN account_name ILIKE 'medical supplies%' OR account_name ILIKE 'vaccines%'
        THEN amount ELSE 0 END) AS medical_expenses,
  SUM(CASE
        WHEN path ILIKE 'Expenses%' AND NOT (
             path ILIKE '%Payroll Expenses%'
          OR account_name ILIKE 'salary %'
          OR account_name ILIKE 'payroll %'
          OR account_name ILIKE 'workmans comp%'
          OR account_name ILIKE 'profit sharing%'
          OR account_name ILIKE 'medical supplies%'
          OR account_name ILIKE 'vaccines%'
        )
        THEN amount ELSE 0 END) AS operating_expenses,
  0::numeric(18,2) AS other_expenses,  -- adjust if you want a separate "Other" bucket
  SUM(amount) AS total_expenses
FROM exp
GROUP BY report_id
  `;
  const { rows } = await pg.query(sql, [reportId]);
  const t = rows[0];

  await pg.query(
    `
    INSERT INTO pnl_expense_totals (report_id, operating_expenses, payroll_expenses, medical_expenses, other_expenses, total_expenses)
    VALUES ($1,$2,$3,$4,$5,$6)
    ON CONFLICT (report_id) DO UPDATE SET
      operating_expenses = EXCLUDED.operating_expenses,
      payroll_expenses   = EXCLUDED.payroll_expenses,
      medical_expenses   = EXCLUDED.medical_expenses,
      other_expenses     = EXCLUDED.other_expenses,
      total_expenses     = EXCLUDED.total_expenses;
  `,
    [
      reportId,
      t.operating_expenses,
      t.payroll_expenses,
      t.medical_expenses,
      t.other_expenses,
      t.total_expenses,
    ]
  );

  console.log(`✅ Saved expense rollup for report_id=${reportId}`);
  await pg.end();
}

main().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
