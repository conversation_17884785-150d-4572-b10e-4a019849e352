#!/usr/bin/env node
/**
 * Ingest PnL report into Postgres, mapping account IDs to names from the accounts table.
 * - Creates schema tables if not exist
 * - Stores one row in pnl_reports (header/meta + raw JSON)
 * - Stores leaf rows in pnl_lines (Path, AccountName, Amount) with optional account_id
 * - Stores section totals in pnl_summaries (Group, Label, Path, Amount)
 *
 * Usage:
 *   node ingest_pnl.js
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
  QBO_BASE,
  QBO_COMPANY_ID,
  QBO_ACCESS_TOKEN,
  PNL_START,
  PNL_END,
} = process.env;

if (
  !QBO_BASE ||
  !QBO_COMPANY_ID ||
  !QBO_ACCESS_TOKEN ||
  !PNL_START ||
  !PNL_END
) {
  console.error(
    "Missing env vars. Need QBO_BASE, QBO_COMPANY_ID, QBO_ACCESS_TOKEN, PNL_START, PNL_END"
  );
  process.exit(1);
}

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

async function ensureSchema() {
  await pg.query(`
    create table if not exists pnl_reports (
      id bigserial primary key,
      report_name text,
      report_basis text,
      start_date date,
      end_date date,
      currency text,
      generated_at timestamptz,
      raw jsonb,
      created_at timestamptz not null default now()
    );
  `);
  await pg.query(`
    create table if not exists pnl_lines (
      id bigserial primary key,
      report_id bigint not null references pnl_reports(id) on delete cascade,
      path text,
      account_name text,
      account_id text, -- kept for traceability; not used for display
      amount numeric(18,2)
    );
  `);
  await pg.query(`
    create table if not exists pnl_summaries (
      id bigserial primary key,
      report_id bigint not null references pnl_reports(id) on delete cascade,
      "group" text,
      label text,
      path text,
      amount numeric(18,2)
    );
  `);
}

async function fetchPnL(startDate, endDate) {
  const url = `${QBO_BASE}/v3/company/${QBO_COMPANY_ID}/reports/ProfitAndLoss?start_date=${encodeURIComponent(
    startDate
  )}&end_date=${encodeURIComponent(endDate)}&summarize_column_by=Total`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${QBO_ACCESS_TOKEN}`,
      Accept: "application/json",
    },
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(
      `QBO PnL failed: ${res.status} ${res.statusText} – ${text}`
    );
  }
  return res.json();
}

function parseAmount(v) {
  if (v === null || v === undefined) return null;
  const s = String(v).replace(/,/g, "").trim();
  if (!s) return null;
  const n = Number(s);
  return Number.isFinite(n) ? n : null;
}

function flattenPnL(pnlJson) {
  const flat = [];
  const summaries = [];

  function walkRows(node, pathParts = []) {
    if (!node) return;
    const rows = node.Row || [];
    for (const r of rows) {
      const rType = r.type;

      // extend path via Header label
      let newPath = pathParts.slice();
      if (r.Header?.ColData?.length) {
        const headerLabel = r.Header.ColData[0]?.value || "";
        if (headerLabel) newPath.push(headerLabel);
      }

      if (rType === "Data") {
        const cd = r.ColData || [];
        const rawName = cd[0]?.value || "";
        const acctId = cd[0]?.id ? String(cd[0].id) : null;
        const amount = parseAmount(cd[1]?.value);
        flat.push({
          path: newPath.join(" > "),
          account_label: rawName,
          account_id: acctId,
          amount,
        });
      }

      if (rType === "Section" && r.Summary?.ColData?.length >= 2) {
        const s = r.Summary.ColData;
        summaries.push({
          path: newPath.join(" > "),
          label: s[0]?.value || "",
          amount: parseAmount(s[1]?.value || ""),
          group: r.group || "",
        });
      }

      if (r.Rows) walkRows(r.Rows, newPath);
    }
  }

  walkRows(pnlJson?.Rows || {}, []);
  return { flat, summaries };
}

async function loadAccountMap() {
  const res = await pg.query(`select id, name from accounts`);
  const map = new Map();
  for (const r of res.rows) map.set(String(r.id), r.name);
  return map;
}

async function insertReportHeader(pnl) {
  const h = pnl.Header || {};
  const q = `
    insert into pnl_reports (report_name, report_basis, start_date, end_date, currency, generated_at, raw)
    values ($1, $2, $3, $4, $5, $6, $7)
    returning id
  `;
  const vals = [
    h.ReportName || null,
    h.ReportBasis || null,
    h.StartPeriod || null,
    h.EndPeriod || null,
    h.Currency || null,
    h.Time || null,
    pnl,
  ];
  const r = await pg.query(q, vals);
  return r.rows[0].id;
}

async function insertLines(reportId, rows, accountMap) {
  if (!rows.length) return;
  // Map account_id -> account_name; fallback to account_label
  const prepared = rows.map((r) => ({
    path: r.path,
    account_name:
      r.account_id && accountMap.get(r.account_id)
        ? accountMap.get(r.account_id)
        : r.account_label,
    account_id: r.account_id,
    amount: r.amount,
  }));

  const cols = ["report_id", "path", "account_name", "account_id", "amount"];
  const values = [];
  const placeholders = prepared
    .map((row, i) => {
      const base = i * cols.length;
      values.push(
        reportId,
        row.path,
        row.account_name,
        row.account_id,
        row.amount
      );
      return `($${base + 1}, $${base + 2}, $${base + 3}, $${base + 4}, $${
        base + 5
      })`;
    })
    .join(",");

  const sql = `insert into pnl_lines (${cols.join(
    ","
  )}) values ${placeholders};`;
  await pg.query(sql, values);
}

async function insertSummaries(reportId, summaries) {
  if (!summaries.length) return;
  const cols = ["report_id", '"group"', "label", "path", "amount"];
  const values = [];
  const placeholders = summaries
    .map((s, i) => {
      const base = i * cols.length;
      values.push(reportId, s.group, s.label, s.path, s.amount);
      return `($${base + 1}, $${base + 2}, $${base + 3}, $${base + 4}, $${
        base + 5
      })`;
    })
    .join(",");
  const sql = `insert into pnl_summaries (${cols.join(
    ","
  )}) values ${placeholders};`;
  await pg.query(sql, values);
}

async function run() {
  await pg.connect();
  await ensureSchema();

  // 1) Fetch report
  const pnl = await fetchPnL(PNL_START, PNL_END);

  // 2) Flatten
  const { flat, summaries } = flattenPnL(pnl);

  // 3) Insert header row (report) and get report_id
  const reportId = await insertReportHeader(pnl);

  // 4) Load Accounts map from DB, map account_id -> name
  const accountMap = await loadAccountMap();

  // 5) Insert detail lines and summaries
  await insertLines(reportId, flat, accountMap);
  await insertSummaries(reportId, summaries);

  // 6) Optional: quick reconciliation print
  const totIncome = flat
    .filter((r) => (r.path || "").startsWith("Income"))
    .reduce((a, r) => a + (Number.isFinite(r.amount) ? r.amount : 0), 0);
  const totExpenses = flat
    .filter((r) => (r.path || "").startsWith("Expenses"))
    .reduce((a, r) => a + (Number.isFinite(r.amount) ? r.amount : 0), 0);
  const calcNet = totIncome - totExpenses;
  const reportedNet =
    summaries.find((s) => s.group === "NetIncome")?.amount ?? null;

  console.log(`✅ PnL saved. report_id=${reportId}`);
  console.log(`   Total Income (calc): ${totIncome.toFixed(2)}`);
  console.log(`   Total Expenses (calc): ${totExpenses.toFixed(2)}`);
  console.log(
    `   Net Income (calc): ${calcNet.toFixed(2)} | Reported: ${
      reportedNet ?? "N/A"
    }`
  );

  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
