#!/usr/bin/env node
import "dotenv/config";
import { Client } from "pg";

const {
  QBO_BASE,
  QBO_COMPANY_ID,
  QBO_ACCESS_TOKEN,
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
} = process.env;

const RANGE_START = process.argv[2] || process.env.CF_START;
const RANGE_END = process.argv[3] || process.env.CF_END;

if (
  !QBO_BASE ||
  !QBO_COMPANY_ID ||
  !QBO_ACCESS_TOKEN ||
  !RANGE_START ||
  !RANGE_END
) {
  console.error(
    "Missing env/args. Need QBO_BASE, QBO_COMPANY_ID, QBO_ACCESS_TOKEN, CF_START, CF_END."
  );
  process.exit(1);
}

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PG<PERSON><PERSON>,
  password: PGPASSWORD,
});

function addMonths(dateStr, n) {
  const d = new Date(dateStr + "T00:00:00Z");
  d.setUTCMonth(d.getUTCMonth() + n);
  return d.toISOString().slice(0, 10);
}
function monthRange(start, end) {
  const out = [];
  let cur = start.slice(0, 7) + "-01";
  while (cur <= end) {
    const next = addMonths(cur, 1);
    const monthEnd = new Date(
      new Date(next + "T00:00:00Z").getTime() - 86400000
    )
      .toISOString()
      .slice(0, 10);
    out.push({ start: cur, end: monthEnd > end ? end : monthEnd });
    cur = next;
  }
  return out;
}

async function ensureSchema() {
  await pg.query(`
    CREATE TABLE IF NOT EXISTS cashflow_reports (
      id BIGSERIAL PRIMARY KEY,
      report_name TEXT,
      report_basis TEXT,
      start_date DATE,
      end_date DATE,
      currency TEXT,
      generated_at TIMESTAMPTZ,
      raw JSONB,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );
  `);
  await pg.query(`
    CREATE TABLE IF NOT EXISTS cashflow_lines (
      id BIGSERIAL PRIMARY KEY,
      report_id BIGINT NOT NULL REFERENCES cashflow_reports(id) ON DELETE CASCADE,
      path TEXT,
      label TEXT,
      "group" TEXT,
      amount NUMERIC(18,2)
    );
  `);
  await pg.query(`
    CREATE TABLE IF NOT EXISTS cashflow_totals (
      id BIGSERIAL PRIMARY KEY,
      report_id BIGINT NOT NULL UNIQUE REFERENCES cashflow_reports(id) ON DELETE CASCADE,
      operating NUMERIC(18,2) NOT NULL DEFAULT 0,
      investing NUMERIC(18,2) NOT NULL DEFAULT 0,
      financing NUMERIC(18,2) NOT NULL DEFAULT 0,
      net_cash_flow NUMERIC(18,2) NOT NULL DEFAULT 0,
      beginning_cash NUMERIC(18,2) NOT NULL DEFAULT 0,
      ending_cash NUMERIC(18,2) NOT NULL DEFAULT 0,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );
  `);
}

async function fetchCashFlow(startDate, endDate) {
  const url = `${QBO_BASE}/v3/company/${QBO_COMPANY_ID}/reports/CashFlow?start_date=${encodeURIComponent(
    startDate
  )}&end_date=${encodeURIComponent(endDate)}&summarize_column_by=Total`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${QBO_ACCESS_TOKEN}`,
      Accept: "application/json",
    },
  });
  if (!res.ok)
    throw new Error(
      `QBO CashFlow ${res.status} ${res.statusText} :: ${await res.text()}`
    );
  return res.json();
}

function parseAmount(v) {
  if (v == null) return null;
  const n = Number(String(v).replace(/,/g, "").trim());
  return Number.isFinite(n) ? n : null;
}

// Map QBO groups/sections to our normalized buckets
function normalizeGroup(rawGroup, headerLabel) {
  const g = String(rawGroup || "").toLowerCase();
  const h = String(headerLabel || "").toLowerCase();

  if (g.includes("operating") || h.includes("operating")) return "Operating";
  if (g.includes("investing") || h.includes("investing")) return "Investing";
  if (g.includes("financing") || h.includes("financing")) return "Financing";
  if (
    g.includes("cashincrease") ||
    h.includes("net cash increase") ||
    h.includes("net cash decrease") ||
    h.includes("net cash")
  )
    return "NetCash";
  if (g.includes("beginningcash") || h.includes("cash at beginning"))
    return "BeginningCash";
  if (g.includes("endingcash") || h.includes("cash at end"))
    return "EndingCash";
  return null;
}

function flattenCashFlow(json) {
  const flat = [];
  const header = json?.Header || {};
  const rowsNode = json?.Rows || {};

  function walk(node, path = [], currentGroup = null) {
    const rows = node?.Row || [];
    for (const r of rows) {
      const t = r.type;
      let newPath = path.slice();
      let group = currentGroup;

      // Header section updates path + maybe group
      const headerLabel = r.Header?.ColData?.[0]?.value || "";
      if (headerLabel) {
        newPath.push(headerLabel);
        const normalized = normalizeGroup(r.group, headerLabel);
        if (normalized) group = normalized;
      }

      if (t === "Data") {
        const cd = r.ColData || [];
        flat.push({
          path: newPath.join(" > "),
          label: cd[0]?.value || "",
          group: normalizeGroup(r.group, cd[0]?.value) || group,
          amount: parseAmount(cd[1]?.value),
        });
      }

      if (t === "Section" && r.Summary?.ColData?.length >= 2) {
        const s = r.Summary.ColData;
        flat.push({
          path: newPath.join(" > "),
          label: s[0]?.value || "",
          group: normalizeGroup(r.group, s[0]?.value) || group,
          amount: parseAmount(s[1]?.value),
        });
      }

      if (r.Rows) walk(r.Rows, newPath, group);

      // Section without Header (some summaries come as Section only)
      if (t === "Section" && !r.Header && r.Summary?.ColData?.length >= 2) {
        const s = r.Summary.ColData;
        flat.push({
          path: newPath.join(" > "),
          label: s[0]?.value || "",
          group: normalizeGroup(r.group, s[0]?.value) || group,
          amount: parseAmount(s[1]?.value),
        });
      }
    }
  }

  walk(rowsNode, [], null);
  return { header, lines: flat };
}

async function insertReport(header, raw) {
  const q = `
    INSERT INTO cashflow_reports (report_name, report_basis, start_date, end_date, currency, generated_at, raw)
    VALUES ($1,$2,$3,$4,$5,$6,$7) RETURNING id
  `;
  const vals = [
    header.ReportName || null,
    header.ReportBasis || null,
    header.StartPeriod || null,
    header.EndPeriod || null,
    header.Currency || null,
    header.Time || null,
    raw,
  ];
  const r = await pg.query(q, vals);
  return r.rows[0].id;
}

async function insertLines(reportId, lines) {
  if (!lines.length) return;
  const cols = ["report_id", "path", "label", '"group"', "amount"];
  const values = [];
  const placeholders = lines
    .map((row, i) => {
      const base = i * cols.length;
      values.push(reportId, row.path, row.label, row.group, row.amount);
      return `($${base + 1},$${base + 2},$${base + 3},$${base + 4},$${
        base + 5
      })`;
    })
    .join(",");
  await pg.query(
    `INSERT INTO cashflow_lines (${cols.join(",")}) VALUES ${placeholders};`,
    values
  );
}

async function upsertTotals(reportId) {
  const { rows } = await pg.query(
    `
    SELECT
      SUM(CASE WHEN "group"='Operating'     THEN amount ELSE 0 END) AS operating,
      SUM(CASE WHEN "group"='Investing'     THEN amount ELSE 0 END) AS investing,
      SUM(CASE WHEN "group"='Financing'     THEN amount ELSE 0 END) AS financing,
      SUM(CASE WHEN "group"='NetCash'       THEN amount ELSE 0 END) AS net_cash_flow,
      SUM(CASE WHEN "group"='BeginningCash' THEN amount ELSE 0 END) AS beginning_cash,
      SUM(CASE WHEN "group"='EndingCash'    THEN amount ELSE 0 END) AS ending_cash
    FROM cashflow_lines
    WHERE report_id = $1
  `,
    [reportId]
  );
  const t = rows[0] || {};
  await pg.query(
    `
    INSERT INTO cashflow_totals (report_id, operating, investing, financing, net_cash_flow, beginning_cash, ending_cash)
    VALUES ($1,$2,$3,$4,$5,$6,$7)
    ON CONFLICT (report_id) DO UPDATE SET
      operating = EXCLUDED.operating,
      investing = EXCLUDED.investing,
      financing = EXCLUDED.financing,
      net_cash_flow = EXCLUDED.net_cash_flow,
      beginning_cash = EXCLUDED.beginning_cash,
      ending_cash = EXCLUDED.ending_cash
  `,
    [
      reportId,
      t.operating || 0,
      t.investing || 0,
      t.financing || 0,
      t.net_cash_flow || 0,
      t.beginning_cash || 0,
      t.ending_cash || 0,
    ]
  );
}

async function run() {
  await pg.connect();
  await ensureSchema();

  const months = monthRange(RANGE_START, RANGE_END);
  let count = 0;

  for (const { start, end } of months) {
    const json = await fetchCashFlow(start, end);
    const { header, lines } = flattenCashFlow(json);
    const reportId = await insertReport(header, json);
    await insertLines(reportId, lines);
    await upsertTotals(reportId);
    console.log(`✅ Cash Flow saved ${start}..${end} (report_id=${reportId})`);
    count++;
  }

  console.log(`Done. ${count} month(s) ingested.`);
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
