#!/usr/bin/env node
/**
 * Ingest Accounts from QBO into Postgres.
 * - Creates table if not exists
 * - Paginates with Query API
 * - Upserts by Account Id
 *
 * Usage:
 *   node ingest_accounts.js
 */

import "dotenv/config";
import { Client } from "pg";

const {
  PGHOST,
  PGPORT,
  PGDATABASE,
  PGUSER,
  PGPASSWORD,
  QBO_BASE,
  QBO_COMPANY_ID,
  QBO_ACCESS_TOKEN,
} = process.env;

if (!QBO_BASE || !QBO_COMPANY_ID || !QBO_ACCESS_TOKEN) {
  console.error(
    "Missing QBO env vars. Set QBO_BASE, QBO_COMPANY_ID, QBO_ACCESS_TOKEN"
  );
  process.exit(1);
}

const pg = new Client({
  host: PGHOST,
  port: Number(PGPORT || 5432),
  database: PGDATABASE,
  user: PGUSER,
  password: PGPASSWORD,
});

async function ensureSchema() {
  await pg.query(`
    create table if not exists accounts (
      id text primary key,
      name text not null,
      fully_qualified_name text,
      account_type text,
      account_sub_type text,
      classification text,
      active boolean,
      currency text,
      raw jsonb,
      updated_at timestamptz not null default now(),
      created_at timestamptz not null default now()
    );
  `);
}

async function qboQueryAccounts(startPosition = 1, maxResults = 500) {
  const q = encodeURIComponent(
    `select * from Account startposition ${startPosition} maxresults ${maxResults}`
  );
  const url = `${QBO_BASE}/v3/company/${QBO_COMPANY_ID}/query?query=${q}`;
  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${QBO_ACCESS_TOKEN}`,
      Accept: "application/json",
    },
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(
      `QBO Accounts query failed: ${res.status} ${res.statusText} – ${text}`
    );
  }
  return res.json();
}

async function upsertAccounts(accounts) {
  if (!accounts?.length) return 0;
  const text = `
    insert into accounts (id, name, fully_qualified_name, account_type, account_sub_type, classification, active, currency, raw, updated_at)
    values ${accounts
      .map(
        (_, i) =>
          `($${i * 10 + 1}, $${i * 10 + 2}, $${i * 10 + 3}, $${i * 10 + 4}, $${
            i * 10 + 5
          }, $${i * 10 + 6}, $${i * 10 + 7}, $${i * 10 + 8}, $${
            i * 10 + 9
          }, now())`
      )
      .join(",")}
    on conflict (id) do update set
      name = excluded.name,
      fully_qualified_name = excluded.fully_qualified_name,
      account_type = excluded.account_type,
      account_sub_type = excluded.account_sub_type,
      classification = excluded.classification,
      active = excluded.active,
      currency = excluded.currency,
      raw = excluded.raw,
      updated_at = now();
  `;
  const values = [];
  for (const a of accounts) {
    values.push(
      String(a.Id ?? ""),
      a.Name ?? null,
      a.FullyQualifiedName ?? null,
      a.AccountType ?? null,
      a.AccountSubType ?? null,
      a.Classification ?? null,
      a.Active ?? null,
      a.CurrencyRef?.value ?? null,
      a
    );
  }
  const r = await pg.query(text, values);
  return r.rowCount;
}

async function run() {
  await pg.connect();
  await ensureSchema();

  let total = 0;
  let start = 1;
  const page = 500;

  // paginate until fewer than page results
  // QBO often returns QueryResponse.MaxResults to help, but we’ll use count.
  // eslint-disable-next-line no-constant-condition
  while (true) {
    const j = await qboQueryAccounts(start, page);
    const list = j?.QueryResponse?.Account || [];
    if (!list.length) break;

    const inserted = await upsertAccounts(list);
    total += inserted;
    if (list.length < page) break;
    start += page;
  }

  console.log(`✅ Upserted ${total} accounts.`);
  await pg.end();
}

run().catch(async (e) => {
  console.error("❌ Failed:", e);
  try {
    await pg.end();
  } catch {}
  process.exit(1);
});
