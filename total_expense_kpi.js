#!/usr/bin/env node
/**
 * Export PowerBI JSON for a given P&L report (or latest).
 *
 * Output structure:
 * {
 *   meta: { report_id, start_date, end_date, currency, generated_at, exported_at },
 *   topline: { revenue, total_expenses, net_income },
 *   expense_breakdown: {
 *     totals: { Operating, Payroll, Medical, Other, Admin, Dues, SalaryExpense, PayrollTaxExpense, HealthInsurance },
 *     lines: [ { account_id, account_name, path, amount, category } ... ]
 *   },
 *   pnl_summaries: [ { label, amount, group } ],
 *   cashflow: { operating, investing, financing, net_cash_flow, beginning_cash, ending_cash } OR null,
 *   kpis: { net_income, interest_expense, income_taxes, depreciation, amortization, ebit, ebitda } OR null
 * }
 *
 * The script expects the following tables/views:
 * - pnl_reports (id, start_date, end_date, currency, generated_at, raw)
 * - pnl_lines (report_id, path, account_id, account_name, amount)
 * - pnl_summaries (report_id, "group", label, amount)
 * - pnl_expense_totals (report_id, operating_expenses, payroll_expenses, medical_expenses, other_expenses, total_expenses)
 * - v_expense_lines_categorized_ext OR v_expense_lines_categorized (view that exposes category per expense line)
 * - cashflow_totals (report_id, operating, investing, financing, net_cash_flow, beginning_cash, ending_cash)
 * - pnl_kpis (report_id, net_income, interest_expense, income_taxes, depreciation, amortization, ebit, ebitda)
 */

import "dotenv/config";
import fs from "fs";
import path from "path";
import { Client } from "pg";

const REPORT_ID_ARG = process.argv[2] ? Number(process.argv[2]) : null;
const OUT_PATH =
  process.argv[3] || path.resolve(process.cwd(), "powerbi_export.json");

const pg = new Client({
  host: process.env.PGHOST || "localhost",
  port: Number(process.env.PGPORT || 5432),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  // optional: add ssl config if needed
});

function safeNum(n) {
  if (n === null || n === undefined) return 0;
  return Number(n);
}

async function getReportId(arg) {
  if (arg) {
    const { rows } = await pg.query(
      "SELECT id FROM pnl_reports WHERE id = $1 LIMIT 1",
      [arg]
    );
    if (!rows.length) throw new Error(`report_id ${arg} not found`);
    return rows[0].id;
  }
  const { rows } = await pg.query(
    "SELECT id FROM pnl_reports ORDER BY start_date DESC, id DESC LIMIT 1"
  );
  if (!rows.length) throw new Error("No pnl_reports found in the database");
  return rows[0].id;
}

async function getReportHeader(reportId) {
  const { rows } = await pg.query(
    `SELECT id, report_name, report_basis, start_date, end_date, currency, generated_at FROM pnl_reports WHERE id = $1 LIMIT 1`,
    [reportId]
  );
  return rows[0] || null;
}

async function getToplineValues(reportId) {
  // revenue: try to read from summaries (Total Income) or compute from income lines
  const qSummary = `
    SELECT "group", label, amount
    FROM pnl_summaries
    WHERE report_id = $1
  `;
  const sumRes = await pg.query(qSummary, [reportId]);

  let totalIncome = null;
  let totalExpenses = null;
  let netIncome = null;
  for (const r of sumRes.rows) {
    if (String(r.label).toLowerCase().includes("total income"))
      totalIncome = safeNum(r.amount);
    if (String(r.label).toLowerCase().includes("total expenses"))
      totalExpenses = safeNum(r.amount);
    if (
      String(r["group"]).toLowerCase() === "netincome" ||
      String(r.label).toLowerCase().includes("net income")
    )
      netIncome = safeNum(r.amount);
  }

  // Fallbacks: try compute from pnl_lines if summaries absent
  if (totalIncome === null) {
    const q = `SELECT SUM(amount) as total FROM pnl_lines WHERE report_id = $1 AND path LIKE 'Income%'`;
    const r = await pg.query(q, [reportId]);
    totalIncome = safeNum(r.rows[0]?.total);
  }
  if (totalExpenses === null) {
    const q = `SELECT SUM(amount) as total FROM pnl_lines WHERE report_id = $1 AND path LIKE 'Expenses%'`;
    const r = await pg.query(q, [reportId]);
    totalExpenses = safeNum(r.rows[0]?.total);
  }
  if (netIncome === null) {
    netIncome = totalIncome - totalExpenses;
  }

  return {
    revenue: totalIncome,
    total_expenses: totalExpenses,
    net_income: netIncome,
  };
}

async function getExpenseBreakdown(reportId) {
  // 1) Try reading persisted totals table
  const { rows: totRows } = await pg.query(
    `
    SELECT operating_expenses, payroll_expenses, medical_expenses, other_expenses, total_expenses
    FROM pnl_expense_totals WHERE report_id = $1 LIMIT 1
  `,
    [reportId]
  );

  const totals = totRows.length
    ? {
        Operating: safeNum(totRows[0].operating_expenses),
        Payroll: safeNum(totRows[0].payroll_expenses),
        Medical: safeNum(totRows[0].medical_expenses),
        Other: safeNum(totRows[0].other_expenses),
        Total: safeNum(totRows[0].total_expenses),
      }
    : null;

  // 2) Get categorized lines from the categorized view if it exists (prefer extended view)
  const categorizedViewCandidates = [
    "v_expense_lines_categorized_ext",
    "v_expense_lines_categorized",
    "v_expense_lines_categorized_view", // fallback name possibility
  ];
  let chosenView = null;
  for (const v of categorizedViewCandidates) {
    const { rows } = await pg.query(
      `SELECT to_regclass($1) IS NOT NULL AS exists`,
      [v]
    );
    if (rows[0]?.exists) {
      chosenView = v;
      break;
    }
  }

  let lines = [];
  if (chosenView) {
    const { rows } = await pg.query(
      `SELECT id, account_id, account_name, path, amount, category FROM ${chosenView} WHERE report_id = $1 ORDER BY category NULLS LAST, amount DESC NULLS LAST`,
      [reportId]
    );
    lines = rows.map((r) => ({
      line_id: r.id,
      account_id: r.account_id,
      account_name: r.account_name,
      path: r.path,
      amount: safeNum(r.amount),
      category: r.category || "Other",
    }));
  } else {
    // fallback: attempt a simple heuristic using pnl_lines + seeded categories in expense_category_map
    // If no view, return raw expense lines and let Power BI or offline mapping apply categories.
    const { rows } = await pg.query(
      `SELECT id, account_id, account_name, path, amount FROM pnl_lines WHERE report_id = $1 AND path LIKE 'Expenses%' ORDER BY amount DESC`,
      [reportId]
    );
    lines = rows.map((r) => ({
      line_id: r.id,
      account_id: r.account_id,
      account_name: r.account_name,
      path: r.path,
      amount: safeNum(r.amount),
      category: null,
    }));
  }

  // If totals missing, aggregate from lines
  const computedTotals = totals
    ? totals
    : (() => {
        const agg = {};
        for (const l of lines) {
          const c = l.category || "Other";
          agg[c] = (agg[c] || 0) + safeNum(l.amount);
        }
        // ensure standard buckets at least exist
        const standard = [
          "Operating",
          "Payroll",
          "Medical",
          "Other",
          "Admin",
          "Dues",
          "Salary Expense",
          "Payroll Tax Expense",
          "Health Insurance",
        ];
        const out = {};
        for (const s of standard) out[s] = safeNum(agg[s] || 0);
        out.Total = Object.values(agg).reduce((a, b) => a + b, 0);
        return out;
      })();

  return { totals: computedTotals, lines };
}

async function getPnlSummaries(reportId) {
  const { rows } = await pg.query(
    `SELECT label, amount, "group" FROM pnl_summaries WHERE report_id = $1 ORDER BY label`,
    [reportId]
  );
  return rows.map((r) => ({
    label: r.label,
    group: r.group,
    amount: safeNum(r.amount),
  }));
}

async function getCashflow(reportHeader) {
  if (!reportHeader) return null;
  // Try find cashflow_totals row for same month (match by month)
  const startDate = reportHeader.start_date;
  if (!startDate) return null;
  const monthStart = startDate; // assuming start_date is first of month for monthly reports
  const { rows } = await pg.query(
    `SELECT operating, investing, financing, net_cash_flow, beginning_cash, ending_cash FROM cashflow_totals WHERE EXISTS (SELECT 1 FROM cashflow_reports cr WHERE cr.id = cashflow_totals.report_id AND cr.start_date = $1) LIMIT 1`,
    [monthStart]
  );
  if (!rows.length) return null;
  const r = rows[0];
  return {
    operating: safeNum(r.operating),
    investing: safeNum(r.investing),
    financing: safeNum(r.financing),
    net_cash_flow: safeNum(r.net_cash_flow),
    beginning_cash: safeNum(r.beginning_cash),
    ending_cash: safeNum(r.ending_cash),
  };
}

async function getKpis(reportId) {
  const { rows } = await pg.query(
    `SELECT net_income, interest_expense, income_taxes, depreciation, amortization, ebit, ebitda FROM pnl_kpis WHERE report_id = $1 LIMIT 1`,
    [reportId]
  );
  if (!rows.length) return null;
  const r = rows[0];
  return {
    net_income: safeNum(r.net_income),
    interest_expense: safeNum(r.interest_expense),
    income_taxes: safeNum(r.income_taxes),
    depreciation: safeNum(r.depreciation),
    amortization: safeNum(r.amortization),
    ebit: safeNum(r.ebit),
    ebitda: safeNum(r.ebitda),
  };
}

async function run() {
  try {
    await pg.connect();
    const reportId = await getReportId(REPORT_ID_ARG);
    const header = await getReportHeader(reportId);
    if (!header) throw new Error("Report header not found");

    const topline = await getToplineValues(reportId);
    const expense = await getExpenseBreakdown(reportId);
    const summaries = await getPnlSummaries(reportId);
    const cashflow = await getCashflow(header);
    const kpis = await getKpis(reportId);

    const out = {
      meta: {
        report_id: reportId,
        report_name: header.report_name || null,
        start_date: header.start_date
          ? header.start_date.toISOString().slice(0, 10)
          : null,
        end_date: header.end_date
          ? header.end_date.toISOString().slice(0, 10)
          : null,
        currency: header.currency || null,
        generated_at: header.generated_at
          ? header.generated_at.toISOString()
          : null,
        exported_at: new Date().toISOString(),
      },
      topline,
      expense_breakdown: {
        totals: expense.totals,
        lines: expense.lines,
      },
      pnl_summaries: summaries,
      cashflow,
      kpis,
    };

    // Write JSON file
    // fs.writeFileSync(OUT_PATH, JSON.stringify(out, null, 2), 'utf8');
    console.log(`✅ Exported PowerBI JSON -> ${OUT_PATH}`);
    await pg.query(
      `UPDATE pnl_reports SET powerbi_kpi_json =${out} WHERE id = $1`,
      [reportId]
    );
    await pg.end();
  } catch (err) {
    console.error("❌ Failed to export PowerBI JSON:", err);
    try {
      await pg.end();
    } catch {}
    process.exit(1);
  }
}

run();
